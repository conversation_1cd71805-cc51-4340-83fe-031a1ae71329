import { Metada<PERSON> } from "next";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Check } from "lucide-react";
import { getServiceBySlug } from "@/lib/services/data";
import { getIconComponent } from "@/lib/utils/icon-mapper";
import { formatPrice, formatPeriod } from "@/lib/utils/icon-mapper";

export const metadata: Metadata = {
  title: "Executive Suites - Via Executive Suites",
  description: "Professional executive suites with premium amenities, reception services, and meeting rooms. Perfect for established businesses seeking a prestigious address in the Rio Grande Valley.",
  keywords: "executive suites, office space, professional workspace, meeting rooms, reception services, Rio Grande Valley",
  openGraph: {
    title: "Executive Suites - Via Executive Suites",
    description: "Professional executive suites with premium amenities, reception services, and meeting rooms. Perfect for established businesses seeking a prestigious address in the Rio Grande Valley.",
    type: "website",
    locale: "en_US",
  },
};

export default function ExecutiveSuitesPage() {
  const service = getServiceBySlug('executive-suites');
  
  if (!service) {
    return <div>Service not found</div>;
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-24 lg:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            {service.name}
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
            {service.shortDescription}
          </p>
          <div className="mt-8">
            <Button size="lg" className="bg-white text-via-primary hover:bg-gray-100 px-8 py-3 mr-4">
              Schedule a Tour
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-via-primary px-8 py-3">
              View Pricing
            </Button>
          </div>
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-16 bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Why Choose Via Executive Suites?
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                {service.longDescription}
              </p>
              <Button size="lg" className="bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3">
                Schedule a Tour
              </Button>
            </div>
            <div className="relative">
              <div className="aspect-w-4 aspect-h-3 rounded-lg overflow-hidden shadow-xl">
                <Image
                  src={service.overviewImage}
                  alt="Professional executive suite interior"
                  width={800}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Succeed
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our executive suites come with comprehensive amenities and services designed to support your business growth.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {service.features.map((feature) => {
              const IconComponent = getIconComponent(feature.icon);
              return (
                <div key={feature.title} className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
                  <div className="w-12 h-12 bg-via-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <IconComponent className="h-6 w-6 text-via-primary" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16 bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Flexible Pricing Plans
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose the perfect plan for your business needs. All plans include our core amenities with flexible terms.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {service.pricingPlans.map((plan) => (
              <div key={plan.id} className={`relative bg-white rounded-lg shadow-lg border-2 overflow-hidden ${
                plan.popular ? 'border-via-primary' : 'border-gray-200'
              }`}>
                {plan.popular && (
                  <div className="absolute top-0 right-0 bg-via-primary text-white px-4 py-2 text-sm font-semibold">
                    Most Popular
                  </div>
                )}
                
                <div className="p-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 mb-4">{plan.description}</p>
                  
                  <div className="mb-6">
                    <span className="text-4xl font-bold text-via-primary">{formatPrice(plan.price, plan.period)}</span>
                    <span className="text-gray-600">{formatPeriod(plan.period)}</span>
                  </div>
                  
                  {plan.sqft && (
                    <div className="mb-4 text-sm text-gray-600">
                      <span className="font-medium">Size:</span> {plan.sqft} sq ft
                      {plan.maxOccupancy && (
                        <span className="ml-4">
                          <span className="font-medium">Max Occupancy:</span> {plan.maxOccupancy} people
                        </span>
                      )}
                    </div>
                  )}
                  
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-center text-gray-700">
                        <Check className="h-5 w-5 text-via-primary mr-3 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button className={`w-full ${
                    plan.popular 
                      ? 'bg-via-primary hover:bg-via-primary-dark text-white' 
                      : 'bg-via-primary hover:bg-via-primary-dark text-white'
                  }`}>
                    Get Started
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-via-primary text-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Elevate Your Business?
          </h2>
          <p className="text-xl text-via-primary-light mb-8 max-w-2xl mx-auto">
            Join the growing number of successful businesses that call Via Executive Suites home. Schedule a tour today and see how we can help your business thrive.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-via-primary hover:bg-gray-100 px-8 py-3">
              Schedule a Tour
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-via-primary px-8 py-3">
              Contact Us
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
