import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { getAllServiceCategories } from "@/lib/services/data";
import { getIconComponent } from "@/lib/utils/icon-mapper";

export const metadata: Metadata = {
  title: "Services - Via Executive Suites",
  description: "Discover our comprehensive range of business services including executive suites, virtual offices, and beauty suites throughout the Rio Grande Valley.",
  keywords: "executive suites, virtual offices, beauty suites, business services, Rio Grande Valley",
  openGraph: {
    title: "Services - Via Executive Suites",
    description: "Discover our comprehensive range of business services including executive suites, virtual offices, and beauty suites throughout the Rio Grande Valley.",
    type: "website",
    locale: "en_US",
  },
};

export default function ServicesPage() {
  const serviceCategories = getAllServiceCategories();

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-24 lg:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Our Services
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
            Comprehensive business solutions designed to help your business thrive in the Rio Grande Valley
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Choose Your Perfect Business Solution
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From fully furnished executive suites to virtual office solutions, we have the perfect option for your business needs.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {serviceCategories.map((category) => {
              const IconComponent = getIconComponent(category.icon);
              return (
                <div key={category.id} className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  <div className={`bg-gradient-to-r ${category.color} p-6 text-white`}>
                    <IconComponent className="h-12 w-12 mb-4" />
                    <h3 className="text-2xl font-bold mb-2">{category.name}</h3>
                    <p className="text-white/90">{category.description}</p>
                  </div>
                  
                  <div className="p-6">
                    <ul className="space-y-2 mb-6">
                      {category.services[0]?.amenities.slice(0, 4).map((amenity) => (
                        <li key={amenity} className="flex items-center text-gray-700">
                          <div className="w-2 h-2 bg-via-primary rounded-full mr-3"></div>
                          {amenity}
                        </li>
                      ))}
                    </ul>
                    
                    <Link href={`/services/${category.slug}`}>
                      <Button className="w-full bg-via-primary hover:bg-via-primary-dark text-white">
                        Learn More
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-50 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Contact us today to schedule a tour of our facilities and find the perfect solution for your business.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3">
              Schedule a Tour
            </Button>
            <Button size="lg" variant="outline" className="border-via-primary text-via-primary hover:bg-via-primary hover:text-white px-8 py-3">
              Contact Us
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
