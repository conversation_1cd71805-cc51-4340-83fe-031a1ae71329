"use client";

import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { fadeInLeft, fadeInRight, animationConfig } from "@/lib/animations";

export function AboutSection() {
  return (
    <section className="py-16 bg-gray-50 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={animationConfig.viewport}
            variants={fadeInLeft}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Office Space for Rent
            </h2>

            <div className="prose prose-lg text-gray-600 mb-8">
              <p className="mb-4">
                Via Executive Suites provides all-inclusive and customizable office space solutions that reduce overhead and costly rental expenses for small businesses. We take pride in helping Rio Grande Valley entrepreneurs and startups achieve success and strengthen the local economy by enhancing their enterprise, identity, and integrity.
              </p>

              <p className="text-lg font-medium text-gray-900">
                We don&apos;t just make work possible. We make better work achievable.
              </p>
            </div>

            <Link href="/about">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  size="lg"
                  className="bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3 text-lg font-semibold transition-all duration-300"
                >
                  About Via Executive Suites
                </Button>
              </motion.div>
            </Link>
          </motion.div>
          
          {/* Image */}
          <motion.div
            className="relative"
            initial="hidden"
            whileInView="visible"
            viewport={animationConfig.viewport}
            variants={fadeInRight}
          >
            <motion.div
              className="aspect-w-4 aspect-h-3 rounded-lg overflow-hidden shadow-xl"
              whileHover={{
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
            >
              <Image
                src="https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
                alt="Modern office space interior"
                width={600}
                height={400}
                className="w-full h-full object-cover"
              />
            </motion.div>

            {/* Decorative elements */}
            <motion.div
              className="absolute -top-4 -left-4 w-24 h-24 bg-via-primary/10 rounded-full -z-10"
              animate={{
                y: [0, -10, 0],
                transition: {
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }
              }}
            />
            <motion.div
              className="absolute -bottom-4 -right-4 w-32 h-32 bg-blue-100 rounded-full -z-10"
              animate={{
                y: [0, 10, 0],
                transition: {
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }
              }}
            />
          </motion.div>
        </div>
      </div>
    </section>
  );
}
