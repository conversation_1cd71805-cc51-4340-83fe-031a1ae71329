import { createClient } from "@/lib/supabase/client";
import { ContactFormData, ContactFormSubmissionResponse } from "@/lib/types/connect";

// Validate email format
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate phone number format (basic validation)
export const isValidPhone = (phone: string): boolean => {
  if (!phone) return true; // Phone is optional
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

// Submit contact form to Supabase
export const submitContactForm = async (
  formData: ContactFormData
): Promise<ContactFormSubmissionResponse> => {
  try {
    const supabase = createClient();

    // Insert form data into Supabase
    const { data, error } = await supabase
      .from('contact_submissions')
      .insert([
        {
          first_name: formData.firstName,
          last_name: formData.lastName,
          email: formData.email,
          phone: formData.phone || null,
          company: formData.company || null,
          message: formData.message,
          submitted_at: new Date().toISOString(),
          status: 'new'
        }
      ])
      .select();

    if (error) {
      console.error('Supabase error:', error);
      return {
        success: false,
        message: 'Failed to submit form. Please try again.',
        errors: { general: error.message }
      };
    }

    // Send notification email (you would implement this with your email service)
    try {
      await sendNotificationEmail(formData);
    } catch (emailError) {
      console.error('Email notification failed:', emailError);
      // Don't fail the form submission if email fails
    }

    return {
      success: true,
      message: 'Thank you for your message! We\'ll get back to you soon.',
      data: data?.[0]
    };

  } catch (error) {
    console.error('Form submission error:', error);
    return {
      success: false,
      message: 'An unexpected error occurred. Please try again.',
      errors: { general: 'Network error' }
    };
  }
};

// Send notification email (placeholder - implement with your email service)
const sendNotificationEmail = async (formData: ContactFormData): Promise<void> => {
  // This would integrate with your email service (SendGrid, Resend, etc.)
  // For now, we'll just log it
  console.log('Sending notification email for:', formData.email);
  
  // Example implementation with a hypothetical email service:
  /*
  const emailData = {
    to: '<EMAIL>',
    subject: `New Contact Form Submission from ${formData.firstName} ${formData.lastName}`,
    html: `
      <h2>New Contact Form Submission</h2>
      <p><strong>Name:</strong> ${formData.firstName} ${formData.lastName}</p>
      <p><strong>Email:</strong> ${formData.email}</p>
      ${formData.phone ? `<p><strong>Phone:</strong> ${formData.phone}</p>` : ''}
      ${formData.company ? `<p><strong>Company:</strong> ${formData.company}</p>` : ''}
      <p><strong>Message:</strong></p>
      <p>${formData.message}</p>
    `
  };
  
  await emailService.send(emailData);
  */
};

// Send auto-reply email to the user
export const sendAutoReply = async (email: string): Promise<void> => {
  // This would send an auto-reply to the user
  console.log(`Sending auto-reply to ${email}`);
  
  // Example implementation:
  /*
  const autoReplyData = {
    to: email,
    subject: 'Thank you for contacting Via Executive Suites',
    html: `
      <h2>Thank you for your inquiry!</h2>
      <p>We've received your message and will get back to you within 24 hours.</p>
      <p>In the meantime, feel free to:</p>
      <ul>
        <li>Browse our <a href="https://viaexecutivesuites.com/services">services</a></li>
        <li>Check out our <a href="https://viaexecutivesuites.com/locations">locations</a></li>
        <li>Call us directly at (*************</li>
      </ul>
      <p>Best regards,<br>The Via Executive Suites Team</p>
    `
  };
  
  await emailService.send(autoReplyData);
  */
};

// Create contact_submissions table schema (for reference)
export const createContactSubmissionsTable = `
  CREATE TABLE IF NOT EXISTS contact_submissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    company VARCHAR(255),
    message TEXT NOT NULL,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'new',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Create index for faster queries
  CREATE INDEX IF NOT EXISTS idx_contact_submissions_email ON contact_submissions(email);
  CREATE INDEX IF NOT EXISTS idx_contact_submissions_status ON contact_submissions(status);
  CREATE INDEX IF NOT EXISTS idx_contact_submissions_submitted_at ON contact_submissions(submitted_at);

  -- Enable RLS (Row Level Security)
  ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;

  -- Create policy for authenticated users to read their own submissions
  CREATE POLICY "Users can view their own submissions" ON contact_submissions
    FOR SELECT USING (auth.uid() IS NOT NULL);

  -- Create policy for inserting new submissions (allow anonymous)
  CREATE POLICY "Anyone can insert contact submissions" ON contact_submissions
    FOR INSERT WITH CHECK (true);
`;

// Utility function to format phone number for display
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  
  // Remove all non-digits
  const digits = phone.replace(/\D/g, '');
  
  // Format as (XXX) XXX-XXXX for US numbers
  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  }
  
  // Return original if not a standard US number
  return phone;
};

// Utility function to sanitize input
export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};
