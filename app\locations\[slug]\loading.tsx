export default function LocationLoading() {
  return (
    <div className="min-h-screen">
      {/* Hero Section Skeleton */}
      <section className="relative bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 py-24 lg:py-32 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-pulse">
            <div className="h-16 bg-gray-300 rounded-lg mb-6 max-w-2xl mx-auto"></div>
            <div className="h-8 bg-gray-300 rounded-lg max-w-3xl mx-auto"></div>
          </div>
        </div>
      </section>

      {/* Content Skeleton */}
      <section className="py-16 bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Left Column Skeleton */}
            <div className="space-y-8">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded-lg mb-6 w-3/4"></div>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-full"></div>
                  <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                  <div className="h-4 bg-gray-200 rounded w-4/6"></div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="h-6 bg-gray-200 rounded-lg w-1/2"></div>
                <div className="space-y-3">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="flex items-center space-x-3">
                      <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="h-10 bg-gray-200 rounded-lg w-24"></div>
                ))}
              </div>
            </div>

            {/* Right Column Skeleton */}
            <div className="space-y-6">
              <div className="h-7 bg-gray-200 rounded-lg w-1/2"></div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                  <div key={i} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-gray-200 rounded-full"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                ))}
              </div>

              <div className="bg-gray-200 rounded-lg h-64"></div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section Skeleton */}
      <section className="py-16 bg-gray-50 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded-lg mb-6 max-w-md mx-auto"></div>
            <div className="h-6 bg-gray-200 rounded-lg mb-8 max-w-2xl mx-auto"></div>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="h-12 bg-gray-200 rounded-lg w-40"></div>
              <div className="h-12 bg-gray-200 rounded-lg w-40"></div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
