import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Connect - Via Executive Suites",
  description: "Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions.",
  keywords: "contact, schedule tour, office space, executive suites, Rio Grande Valley",
  authors: [{ name: "Via Executive Suites" }],
  openGraph: {
    title: "Connect - Via Executive Suites",
    description: "Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Connect - Via Executive Suites",
    description: "Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions.",
  },
};

export default function ConnectLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
