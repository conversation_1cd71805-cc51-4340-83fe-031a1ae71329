# Via Workspace Platform - Technical Context

## Technology Stack

### Frontend Technologies
- **Next.js 15:** React framework with App Router and server components
- **TypeScript 5:** Type-safe JavaScript development
- **React 19:** Latest React with concurrent features
- **Tailwind CSS 3.4:** Utility-first CSS framework
- **shadcn/ui:** Component library built on Radix UI primitives
- **Lucide React:** Icon library for consistent iconography

### Backend Technologies
- **Supabase:** Backend-as-a-Service platform
  - **PostgreSQL 15:** Primary database with advanced features
  - **Supabase Auth:** Authentication and authorization system
  - **Supabase Storage:** File storage with CDN distribution
  - **Supabase Edge Functions:** Serverless functions (Deno runtime)
  - **Supabase Realtime:** Real-time subscriptions and updates

### Development Tools
- **ESLint:** Code linting and quality enforcement
- **Prettier:** Code formatting and consistency
- **pnpm:** Fast package manager for dependencies
- **Git:** Version control with GitHub workflow

## Development Environment

### Local Setup
- **Node.js:** Version 18+ required for Next.js 15
- **pnpm:** Package manager for dependency management
- **Git:** Version control system
- **VS Code:** Recommended IDE with extensions

### Environment Variables
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Google Maps API (Phase 2)
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_key

# Stripe (Phase 3)
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_webhook_secret

# Email Service (Phase 3)
SMTP_HOST=your_smtp_host
SMTP_PORT=your_smtp_port
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
```

### Database Schema
The database will include the following core tables:
- `via_profiles`: Extended user profiles with membership tiers
- `via_locations`: Workspace locations with geographic data
- `via_workspaces`: Individual workspace units with pricing
- `via_bookings`: Booking records with status tracking
- `via_payments`: Payment transactions with Stripe integration
- `via_reviews`: User reviews and ratings with verification
- `via_subscriptions`: Recurring booking subscriptions

## Architecture Decisions

### Why Next.js 15?
- **App Router:** Modern file-based routing with server components
- **Server Components:** Improved performance and SEO
- **TypeScript Support:** Built-in TypeScript support
- **Vercel Integration:** Seamless deployment and optimization

### Why Supabase?
- **PostgreSQL:** Enterprise-grade database with advanced features
- **Real-time:** Built-in real-time subscriptions for availability updates
- **Edge Functions:** Serverless computing for business logic
- **Row Level Security:** Database-level security policies
- **Auto-generated APIs:** REST and GraphQL APIs from database schema

### Why Tailwind CSS + shadcn/ui?
- **Utility-First:** Rapid development and consistent design
- **Component Library:** Pre-built, accessible components
- **Customization:** Easy theme customization and branding
- **Performance:** Optimized CSS with PurgeCSS

## Development Workflow

### Code Organization
```
app/                    # Next.js App Router pages
├── (auth)/            # Authentication routes
├── (dashboard)/       # Protected dashboard routes
├── api/               # API routes
├── globals.css        # Global styles
└── layout.tsx         # Root layout

components/             # Reusable components
├── ui/                # shadcn/ui components
├── forms/             # Form components
├── layout/            # Layout components
└── features/          # Feature-specific components

lib/                   # Utility functions and configurations
├── supabase/          # Supabase client configurations
├── utils/             # Helper functions
├── types/             # TypeScript type definitions
└── constants/         # Application constants
```

### Component Structure
- **Atomic Design:** Atoms → Molecules → Organisms → Templates → Pages
- **Compound Components:** Related components that work together
- **Custom Hooks:** Reusable logic extraction
- **Type Safety:** Full TypeScript coverage

### State Management
- **Zustand:** Lightweight state management for global state
- **Supabase Subscriptions:** Real-time data updates
- **React Query:** Server state management and caching
- **Local State:** Component-specific state with useState

## Performance Considerations

### Frontend Optimization
- **Code Splitting:** Route-based code splitting
- **Image Optimization:** Next.js Image component with Supabase Storage
- **Static Generation:** Pre-rendered pages where possible
- **Bundle Analysis:** Regular bundle size monitoring

### Backend Optimization
- **Database Indexing:** Optimized queries with proper indexes
- **Connection Pooling:** Efficient database connections
- **Caching:** Multi-level caching strategies
- **CDN:** Global content distribution

## Security Implementation

### Authentication
- **JWT Tokens:** Secure session management
- **Social Auth:** Google, GitHub, LinkedIn integration
- **Multi-factor Auth:** Enhanced security for enterprise users
- **Session Management:** Secure session handling

### Data Protection
- **Row Level Security:** Database-level access control
- **Input Validation:** Zod schemas for all inputs
- **SQL Injection Prevention:** Parameterized queries
- **XSS Protection:** Content Security Policy

## Testing Strategy

### Testing Tools
- **Jest:** Unit and integration testing
- **React Testing Library:** Component testing
- **Playwright:** End-to-end testing
- **MSW:** API mocking for testing

### Testing Coverage
- **Unit Tests:** Component and utility function testing
- **Integration Tests:** Supabase operations and API endpoints
- **E2E Tests:** Complete user journey testing
- **Performance Tests:** Core Web Vitals compliance

## Deployment & Infrastructure

### Hosting
- **Frontend:** Vercel with automatic deployments
- **Backend:** Supabase cloud with auto-scaling
- **Database:** Supabase PostgreSQL with backups
- **Storage:** Supabase Storage with global CDN

### CI/CD Pipeline
- **GitHub Actions:** Automated testing and deployment
- **Vercel:** Frontend deployment with previews
- **Supabase:** Database migrations and edge functions
- **Monitoring:** Performance and error tracking

### Environment Management
- **Development:** Local development with Docker
- **Staging:** Supabase staging project
- **Production:** Supabase production project
- **Monitoring:** Real-time performance monitoring
