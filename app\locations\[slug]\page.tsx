"use client";

import { use } from "react";
import { notFound } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { MapPin, Phone, Clock, Mail, Car, Building2 } from "lucide-react";
import { Location, LocationPageProps, LocationSlugs } from "@/lib/types/location";

// Location data - in a real app, this would come from a database
const locationData: Record<LocationSlugs, Location> = {
  "adbc": {
    name: "ADBC",
    fullName: "Via Executive Suites ADBC",
    address: "813 N. Main St., McAllen, Texas 78501",
    phone: "(*************",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    email: "<EMAIL>",
    coordinates: { lat: 26.212138647510635, lng: -98.23348265965645 },
    description: "Located in the heart of McAllen, our ADBC location offers premium office spaces with easy access to major highways and business districts.",
    features: [
      "Private office suites",
      "Conference rooms",
      "Reception services",
      "High-speed internet",
      "Free parking",
      "Break room amenities",
      "Mail handling",
      "24/7 building access"
    ],
    image: "https://via2success.com/wp-content/uploads/2021/01/location-adbc.jpg"
  },
  "la-costa": {
    name: "La Costa",
    fullName: "Via Executive Suites La Costa",
    address: "214 N 16th St, McAllen, Texas 78501",
    phone: "(*************",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    email: "<EMAIL>",
    coordinates: { lat: 26.20654823351708, lng: -98.2359421731485 },
    description: "Our La Costa location provides modern office solutions in a vibrant business community, perfect for growing companies and entrepreneurs.",
    features: [
      "Flexible office spaces",
      "Virtual office services",
      "Meeting facilities",
      "Business support services",
      "Secure access",
      "Professional environment",
      "Networking opportunities",
      "Convenient location"
    ],
    image: "https://via2success.com/wp-content/uploads/2021/01/location-la-costa.jpg"
  },
  "23rd": {
    name: "23rd",
    fullName: "Via Executive Suites 23rd",
    address: "1821 N 23rd Street, McAllen, Texas 78501",
    phone: "(*************",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    email: "<EMAIL>",
    coordinates: { lat: 26.22271557411775, lng: -98.24298783082033 },
    description: "The 23rd Street location offers spacious office environments with premium amenities, ideal for established businesses seeking professional workspace.",
    features: [
      "Executive office suites",
      "Boardroom facilities",
      "Premium amenities",
      "Dedicated support staff",
      "Advanced technology",
      "Professional services",
      "Business networking",
      "Premium location"
    ],
    image: "https://via2success.com/wp-content/uploads/2021/01/location-23rd.jpg"
  },
  "edinburg": {
    name: "Edinburg",
    fullName: "Via Executive Suites Edinburg",
    address: "1409 S 9th Ave, Edinburg, Texas 78539",
    phone: "(*************",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    email: "<EMAIL>",
    coordinates: { lat: 26.28915674741617, lng: -98.16747817499945 },
    description: "Our Edinburg location serves the northern Rio Grande Valley with professional office solutions and comprehensive business support services.",
    features: [
      "Modern office spaces",
      "Collaborative areas",
      "Technology infrastructure",
      "Business services",
      "Community events",
      "Professional development",
      "Local partnerships",
      "Strategic positioning"
    ],
    image: "https://via2success.com/wp-content/uploads/2021/01/location-edinburg.jpg"
  },
  "lindberg": {
    name: "Lindberg",
    fullName: "Via Executive Suites Lindberg",
    address: "1234 Lindberg Ave, McAllen, Texas 78501",
    phone: "(*************",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    email: "<EMAIL>",
    coordinates: { lat: 26.215, lng: -98.240 },
    description: "Our Lindberg location offers premium office solutions in a growing business district with easy access to major transportation routes.",
    features: [
      "Executive suites",
      "Meeting rooms",
      "Business services",
      "Secure access",
      "Parking facilities",
      "High-speed internet",
      "Professional environment",
      "Strategic location"
    ],
    image: "https://via2success.com/wp-content/uploads/2021/01/location-lindberg.jpg"
  }
};

export default function LocationPage({ params }: LocationPageProps) {
  // Unwrap the params Promise using React.use() for Next.js 15 compatibility
  const resolvedParams = use(params) as { slug: string };
  const location = locationData[resolvedParams.slug as LocationSlugs];

  if (!location) {
    notFound();
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-24 lg:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-6">
            {location.fullName}
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
            Professional office solutions in the heart of the Rio Grande Valley
          </p>
        </div>
      </section>

      {/* Location Details */}
      <section className="py-16 bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Location Information */}
            <div className="space-y-8">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  Location Details
                </h2>
                <p className="text-lg text-gray-600 leading-relaxed">
                  {location.description}
                </p>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-gray-900">Contact Information</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-via-primary flex-shrink-0" />
                    <span className="text-gray-700">{location.address}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-via-primary flex-shrink-0" />
                    <span className="text-gray-700">{location.phone}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Clock className="w-5 h-5 text-via-primary flex-shrink-0" />
                    <span className="text-gray-700">{location.hours}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Mail className="w-5 h-5 text-via-primary flex-shrink-0" />
                    <span className="text-gray-700">{location.email}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  onClick={() => window.open(`tel:${location.phone}`, '_self')}
                  className="bg-via-primary hover:bg-via-primary-dark text-white"
                >
                  <Phone className="w-4 h-4 mr-2" />
                  Call Now
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    const encodedAddress = encodeURIComponent(location.address);
                    window.open(`https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`, '_blank');
                  }}
                  className="border-via-primary text-via-primary hover:bg-via-primary hover:text-white"
                >
                  <Car className="w-4 h-4 mr-2" />
                  Get Directions
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.open(`mailto:${location.email}`, '_self')}
                  className="border-via-primary text-via-primary hover:bg-via-primary hover:text-white"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Send Email
                </Button>
              </div>
            </div>

            {/* Features and Amenities */}
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-gray-900">Features & Amenities</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {location.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-via-primary rounded-full"></div>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Location Image Placeholder */}
              <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <Building2 className="w-12 h-12 mx-auto mb-4" />
                  <p className="text-lg font-medium">Location Image</p>
                  <p className="text-sm">Image would be displayed here</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-50 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Contact us today to schedule a tour of {location.fullName} and discover how we can support your business growth.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3"
            >
              Schedule a Tour
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-via-primary text-via-primary hover:bg-via-primary hover:text-white px-8 py-3"
            >
              Contact Us
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
