import Image from "next/image";
import { Check } from "lucide-react";

const includedServices = [
  "Office space",
  "Shared common areas (e.g. Break rooms, conference rooms)",
  "Front desk services",
  "Utilities",
  "Security cameras",
  "Parking lot",
  "Cleaning",
  "Starbucks coffee",
  "And much more"
];

export function AllInclusiveServices() {
  return (
    <section className="py-16 bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Content */}
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              All-Inclusive Executive Suites
            </h2>
            
            <div className="prose prose-lg text-gray-600 mb-8">
              <p className="leading-relaxed mb-6">
                VIA Executive Suites enhances the affordability and accessibility of professional workspaces with all-inclusive solutions. For one price, you can enjoy beautiful, modern office spaces, along with an array of amenities and value-added features that create a business office where your company can grow.
              </p>
              
              <p className="font-semibold text-gray-900 mb-4">
                Our all-inclusive prices include the following:
              </p>
            </div>

            {/* Services List */}
            <div className="space-y-3 mb-8">
              {includedServices.map((service, index) => (
                <div key={index} className="flex items-start">
                  <div className="flex-shrink-0 mr-3 mt-1">
                    <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                      <Check className="w-3 h-3 text-white" />
                    </div>
                  </div>
                  <span className="text-gray-700">{service}</span>
                </div>
              ))}
            </div>

            {/* Additional Services */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-semibold text-gray-900 mb-3">
                Additional Services Available:
              </h3>
              <p className="text-gray-600 mb-4">
                For businesses in need of additional services, we also provide mediation rooms, private offices, and flexible furniture rentals for additional fees.
              </p>
              <p className="text-gray-700 font-medium">
                Enjoy access to everything you need for an affordable price when you choose to rent office space at any of our 4 business centers throughout the RGV.
              </p>
            </div>
          </div>
          
          {/* Image */}
          <div className="relative">
            <div className="aspect-w-4 aspect-h-5 rounded-lg overflow-hidden shadow-xl">
              <Image
                src="https://viatosuccess.com/wp-content/uploads/2021/01/about_us-section_3-img_1.jpg"
                alt="All-inclusive executive office suite"
                width={500}
                height={600}
                className="w-full h-full object-cover"
              />
            </div>
            
            {/* Decorative elements */}
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-primary/10 rounded-full -z-10"></div>
            <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-blue-100 rounded-full -z-10"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
