import { LucideIcon } from "lucide-react";

// Contact form data interface
export interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  company?: string;
  message: string;
}

// Form validation errors interface
export interface ContactFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  company?: string;
  message?: string;
  general?: string;
}

// Form submission state interface
export interface FormSubmissionState {
  isLoading: boolean;
  isSuccess: boolean;
  error: string | null;
}

// Contact information interface
export interface ContactInfo {
  title: string;
  value: string;
  description: string;
  icon: LucideIcon;
  href?: string;
}

// Location information interface
export interface LocationInfo {
  name: string;
  address: string;
  phone: string;
  hours?: string;
  mapUrl?: string;
}

// Contact form component props
export interface ContactFormProps {
  onSubmit?: (data: ContactFormData) => Promise<void>;
  className?: string;
  showCompanyField?: boolean;
  showPhoneField?: boolean;
}

// Contact section props
export interface ContactSectionProps {
  title: string;
  subtitle?: string;
  contactInfo: ContactInfo[];
  className?: string;
}

// Location section props
export interface LocationSectionProps {
  title: string;
  subtitle?: string;
  locations: LocationInfo[];
  className?: string;
}

// Hero section props
export interface HeroSectionProps {
  title: string;
  subtitle: string;
  primaryButtonText: string;
  secondaryButtonText: string;
  onPrimaryClick?: () => void;
  onSecondaryClick?: () => void;
  className?: string;
}

// CTA section props
export interface CTASectionProps {
  title: string;
  subtitle: string;
  primaryButtonText: string;
  secondaryButtonText: string;
  onPrimaryClick?: () => void;
  onSecondaryClick?: () => void;
  className?: string;
}

// Animation variants for connect page
export interface ConnectPageAnimationVariants {
  container: {
    hidden: { opacity: number; y: number };
    visible: { opacity: number; y: number; transition: { duration: number; staggerChildren: number } };
  };
  item: {
    hidden: { opacity: number; y: number };
    visible: { opacity: number; y: number; transition: { duration: number } };
  };
  form: {
    hidden: { opacity: number; scale: number };
    visible: { opacity: number; scale: number; transition: { duration: number; delay: number } };
  };
  button: {
    hidden: { opacity: number; x: number };
    visible: { opacity: number; x: number; transition: { duration: number; delay: number } };
  };
}

// Form field configuration
export interface FormFieldConfig {
  name: keyof ContactFormData;
  label: string;
  type: 'text' | 'email' | 'tel' | 'textarea';
  placeholder?: string;
  required: boolean;
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    customValidator?: (value: string) => string | null;
  };
}

// API response interfaces
export interface ContactFormSubmissionResponse {
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
  errors?: ContactFormErrors;
}

// Toast notification interface
export interface ToastNotification {
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message: string;
  duration?: number;
}
