"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, User, ArrowRight, Clock, Tag } from "lucide-react";

// Blog post interface
interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  author: string;
  date: string;
  category: string;
  readTime: string;
  image: string;
  featured?: boolean;
}

// Sample blog posts with comprehensive content
const blogPosts: BlogPost[] = [
  {
    id: 1,
    title: "5 Traits of a Successful Entrepreneur: Do you have what it takes?",
    excerpt: "Discover the essential characteristics that separate successful entrepreneurs from the rest. Learn how to develop these crucial traits and apply them to your business journey.",
    author: "Via Team",
    date: "December 15, 2024",
    category: "Business Tips",
    readTime: "5 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg",
    featured: true
  },
  {
    id: 2,
    title: "How Much Does It Cost to Rent an Office in the RGV Area",
    excerpt: "Get a comprehensive breakdown of office rental costs in the Rio Grande Valley. Compare different options and find the perfect workspace for your budget.",
    author: "Via Team",
    date: "December 12, 2024",
    category: "Market Insights",
    readTime: "7 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/about_us-section_1-img_1.jpg"
  },
  {
    id: 3,
    title: "5 Benefits of Bilingual Answering Services",
    excerpt: "Explore how bilingual answering services can expand your business reach and improve customer satisfaction in diverse markets like the Rio Grande Valley.",
    author: "Via Team",
    date: "December 10, 2024",
    category: "Business Growth",
    readTime: "4 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
  },
  {
    id: 4,
    title: "How to Find Small Business Space for Rent in McAllen",
    excerpt: "A complete guide to finding the perfect small business space in McAllen. Tips, locations, and what to look for when choosing your next office.",
    author: "Via Team",
    date: "December 8, 2024",
    category: "Location Guide",
    readTime: "6 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/about_us-section_1-img_1.jpg"
  },
  {
    id: 5,
    title: "When to Use a Virtual Business Address vs Executive Office",
    excerpt: "Understanding the differences between virtual addresses and executive offices. Make the right choice for your business needs and budget.",
    author: "Via Team",
    date: "December 5, 2024",
    category: "Business Solutions",
    readTime: "5 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
  },
  {
    id: 6,
    title: "Top 5 Reasons Why It's Better to Lease Office Space",
    excerpt: "Discover the advantages of leasing office space over buying. From flexibility to cost savings, learn why leasing might be the smart choice for your business.",
    author: "Via Team",
    date: "December 3, 2024",
    category: "Business Strategy",
    readTime: "4 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
  },
  {
    id: 7,
    title: "What Should you Look for in Meeting Rooms in McAllen?",
    excerpt: "Essential features to consider when booking meeting rooms in McAllen. From technology to location, ensure your meetings are productive and professional.",
    author: "Via Team",
    date: "November 28, 2024",
    category: "Meeting Solutions",
    readTime: "3 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
  },
  {
    id: 8,
    title: "Why Rent a Fully Furnished Office Space?",
    excerpt: "The benefits of choosing fully furnished office spaces for your business. Save time, money, and hassle while maintaining a professional appearance.",
    author: "Via Team",
    date: "November 25, 2024",
    category: "Office Solutions",
    readTime: "5 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
  },
  {
    id: 9,
    title: "Pros and Cons of Home Office vs Executive Office Rentals",
    excerpt: "Compare the advantages and disadvantages of working from home versus renting an executive office. Make an informed decision for your business.",
    author: "Via Team",
    date: "November 22, 2024",
    category: "Workspace Comparison",
    readTime: "6 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
  },
  {
    id: 10,
    title: "5 Tips for Renting Your First Office Space in McAllen",
    excerpt: "First-time office renter? Get expert advice on what to look for, questions to ask, and how to negotiate the best deal for your new business space.",
    author: "Via Team",
    date: "November 20, 2024",
    category: "Getting Started",
    readTime: "7 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
  },
  {
    id: 11,
    title: "The Rise of Coworking Spaces in South Texas",
    excerpt: "Explore how coworking spaces are transforming the business landscape in South Texas and why they're becoming the preferred choice for modern professionals.",
    author: "Via Team",
    date: "November 18, 2024",
    category: "Industry Trends",
    readTime: "6 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
  },
  {
    id: 12,
    title: "Networking Tips for Executive Suite Tenants",
    excerpt: "Maximize your business connections and build valuable relationships with fellow professionals in your executive suite building.",
    author: "Via Team",
    date: "November 15, 2024",
    category: "Networking",
    readTime: "4 min read",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
  }
];

export function BlogSection() {
  return (
    <>
      {/* Blog Posts Grid */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Latest Business Insights
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Stay ahead of the curve with expert advice, industry trends, and practical tips 
              to help your business succeed in the Rio Grande Valley and beyond.
            </p>
          </div>

          {/* Blog Posts Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => (
              <article
                key={post.id}
                className={`bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden group cursor-pointer relative hover:shadow-xl transition-all duration-300 hover:-translate-y-2 ${
                  post.featured ? 'md:col-span-2 lg:col-span-1' : ''
                }`}
              >
                {/* Featured Badge */}
                {post.featured && (
                  <div className="absolute top-4 left-4 z-10 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                    Featured
                  </div>
                )}

                {/* Image Container */}
                <div className="relative overflow-hidden">
                  <img
                    src={post.image}
                    alt={post.title}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                
                {/* Content */}
                <div className="p-6">
                  {/* Category and Read Time */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                    <span className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-xs font-medium flex items-center">
                      <Tag className="h-3 w-3 mr-1" />
                      {post.category}
                    </span>
                    <span className="flex items-center text-gray-400">
                      <Clock className="h-3 w-3 mr-1" />
                      {post.readTime}
                    </span>
                  </div>
                  
                  {/* Title */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
                    {post.title}
                  </h3>
                  
                  {/* Excerpt */}
                  <p className="text-gray-600 mb-4 line-clamp-3 leading-relaxed">
                    {post.excerpt}
                  </p>
                  
                  {/* Meta Information */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      {post.author}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {post.date}
                    </div>
                  </div>
                  
                  {/* Read More Button */}
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white group-hover:shadow-lg transition-all duration-300">
                    Read More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Stay in the Loop
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Get the latest business insights, office space tips, and industry updates 
              delivered straight to your inbox.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent transition-all duration-300"
              />
              <Button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-300">
                Subscribe
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
