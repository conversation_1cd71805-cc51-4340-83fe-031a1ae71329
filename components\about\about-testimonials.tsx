import { Star, Quote } from "lucide-react";

const testimonials = [
  {
    quote: "That moments when you realize everything will be just fine in our new location! I received a call from <PERSON><PERSON><PERSON>, the manager at ADBC, to tell me new customers are looking for us already in the new location!! He forwarded the customer to us! Thank you <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> for all your good customer service. Looking forward for great business for all.",
    author: "Vital Camera Mary",
    company: "Vital Camera",
    rating: 5
  },
  {
    quote: "<PERSON><PERSON><PERSON> and all the staff at ADBC have been extremely helpful since I started Pronto Credit in McAllen on 2011.",
    author: "<PERSON>",
    company: "Pronto Credit",
    rating: 5
  }
];

export function AboutTestimonials() {
  return (
    <section className="py-16 bg-gray-50 border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Testimonials
          </h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="bg-white rounded-lg shadow-md p-8 hover:shadow-lg transition-all duration-300"
            >
              {/* Quote Icon */}
              <div className="flex justify-center mb-6">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full">
                  <Quote className="w-6 h-6 text-primary" />
                </div>
              </div>

              {/* Star Rating */}
              <div className="flex justify-center mb-4">
                {[...Array(testimonial.rating)].map((_, starIndex) => (
                  <Star 
                    key={starIndex} 
                    className="w-5 h-5 text-yellow-400 fill-current" 
                  />
                ))}
              </div>

              {/* Quote Text */}
              <blockquote className="text-center mb-6">
                <p className="text-gray-700 leading-relaxed italic">
                  &ldquo;{testimonial.quote}&rdquo;
                </p>
              </blockquote>

              {/* Attribution */}
              <div className="text-center">
                <div className="font-semibold text-gray-900 text-lg">
                  {testimonial.author}
                </div>
                <div className="text-primary text-sm">
                  {testimonial.company}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
