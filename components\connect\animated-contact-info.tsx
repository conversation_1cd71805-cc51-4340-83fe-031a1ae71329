"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface ContactInfo {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  value: string;
  description: string;
  href?: string;
}

interface ContactSectionProps {
  title: string;
  subtitle?: string;
  contactInfo: ContactInfo[];
  className?: string;
}

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3,
    },
  },
};

const staggerItem = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut" as const,
    },
  },
};

interface AnimatedContactCardProps {
  info: ContactInfo;
}

function AnimatedContactCard({ info }: AnimatedContactCardProps) {
  const IconComponent = info.icon;
  
  return (
    <motion.div
      className="text-center group"
      variants={staggerItem}
              whileHover={{
          y: -8,
          transition: { duration: 0.2, ease: "easeOut" as const }
        }}
    >
      <motion.div 
        className="w-16 h-16 bg-via-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-via-primary/20 transition-colors duration-300"
        whileHover={{
          scale: 1.1,
          rotate: 5,
          transition: { duration: 0.2 }
        }}
      >
        <IconComponent className="h-8 w-8 text-via-primary" />
      </motion.div>
      
      <motion.h3 
        className="text-lg font-semibold text-gray-900 mb-2"
        whileHover={{
          scale: 1.05,
          transition: { duration: 0.2 }
        }}
      >
        {info.title}
      </motion.h3>
      
      <motion.div
        initial={{ opacity: 0.8 }}
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      >
        {info.href ? (
          <a 
            href={info.href}
            className="text-via-primary font-medium mb-2 block hover:text-via-primary-dark transition-colors duration-200"
          >
            {info.value}
          </a>
        ) : (
          <p className="text-via-primary font-medium mb-2">{info.value}</p>
        )}
        <p className="text-gray-600 text-sm">{info.description}</p>
      </motion.div>
    </motion.div>
  );
}

export function AnimatedContactInfo({
  title,
  subtitle,
  contactInfo,
  className
}: ContactSectionProps) {
  return (
    <motion.section
      className={cn("py-12 sm:py-16 lg:py-20 bg-white overflow-hidden", className)}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      variants={staggerContainer}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12 sm:mb-16"
          variants={staggerItem}
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {title}
          </h2>
          {subtitle && (
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
              {subtitle}
            </p>
          )}
        </motion.div>

        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8"
          variants={staggerContainer}
        >
          {contactInfo.map((info) => (
            <AnimatedContactCard
              key={info.title}
              info={info}
            />
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
}
