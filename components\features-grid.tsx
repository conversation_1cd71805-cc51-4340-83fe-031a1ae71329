"use client";

import { motion } from "framer-motion";
import { Building2, Layers, MapPin, DollarSign, Users, TrendingUp, Network, Star } from "lucide-react";
import { fadeInUp, staggerContainer, staggerItem, animationConfig } from "@/lib/animations";

const features = [
  {
    icon: Building2,
    title: "Office Suites",
    description: "Choose from four strategically located, all-inclusive office suites and business centers that include convenient common areas, state-of-the-art workspaces, and suitable prices for all budgets."
  },
  {
    icon: Layers,
    title: "Workspace Variety",
    description: "Choose from a variety of workspaces (including mobile executive suites) to customize your office environment and make your business more efficient."
  },
  {
    icon: MapPin,
    title: "Conveniently Located",
    description: "Work in the heart of the Arts District in McAllen; grow your business in downtown Edinburg; take your enterprise to new heights in La Costa, or relocate your company to McAllen. All of our locations are chosen for convenience and access."
  },
  {
    icon: DollarSign,
    title: "Inclusive Pricing",
    description: "Pay a single price to enjoy everything our workspaces have to offer. Enjoy a business address, waiting room, front desk staff, conference rooms, break rooms, wi-fi, amenities, and more, for a single affordable price."
  },
  {
    icon: Users,
    title: "Dedicated Personnel",
    description: "Bilingual staff provide professional and courteous service to make you and your guests feel welcome around the clock."
  },
  {
    icon: TrendingUp,
    title: "Productivity",
    description: "Help your team reach maximum productivity with quality services, fully furnished workspaces, state-of-the-art lighting, and ambient music."
  },
  {
    icon: Network,
    title: "Networking",
    description: "Build valuable partnerships with networking events aimed at generating business opportunities through communication and synergy."
  },
  {
    icon: Star,
    title: "Added Values",
    description: "Enjoy an array of amenities designed to create a more enjoyable workspace. Meditation rooms, complimentary Starbucks coffee, patio space, and more make better work possible in each of our locations."
  }
];

export function FeaturesGrid() {
  return (
    <section className="py-16 bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial="hidden"
          whileInView="visible"
          viewport={animationConfig.viewport}
          variants={fadeInUp}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Your Perfect Office Spaces in the valley
          </h2>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Create an easier path to business growth through Via Executive Suites. Designed to promote the success of local startups and economies,
            our beautiful, professional, and affordable office spaces offer an array of possibilities to meet your every need.
            From furnished private offices, business center and conference rooms, to coworking and virtual offices,
            we provide a unique, dynamic solution to your office space rental needs.
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          initial="hidden"
          whileInView="visible"
          viewport={animationConfig.viewport}
          variants={staggerContainer}
        >
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <motion.div
                key={index}
                className="text-center p-6 rounded-lg hover:shadow-lg transition-shadow duration-300 group"
                variants={staggerItem}
                whileHover={{
                  y: -8,
                  transition: { duration: 0.2 }
                }}
              >
                <motion.div
                  className="inline-flex items-center justify-center w-16 h-16 bg-via-primary/10 rounded-full mb-4 group-hover:bg-via-primary/20 transition-colors duration-300"
                  whileHover={{
                    scale: 1.1,
                    rotate: 5,
                    transition: { duration: 0.2 }
                  }}
                >
                  <IconComponent className="w-8 h-8 text-via-primary" />
                </motion.div>
                
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
}
