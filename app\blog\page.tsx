import { Metadata } from "next";
import { BlogPageClient } from "./blog-client";

export const metadata: Metadata = {
  title: "Latest News & Insights - Via Executive Suites",
  description: "Stay updated with the latest news, insights, and tips for business success from Via Executive Suites. Expert advice on office spaces, entrepreneurship, and business growth.",
  keywords: "business blog, office space tips, entrepreneurship, business insights, Rio Grande Valley, executive suites, virtual offices, workspace trends",
  openGraph: {
    title: "Latest News & Insights - Via Executive Suites",
    description: "Stay updated with the latest news, insights, and tips for business success from Via Executive Suites.",
    type: "website",
    locale: "en_US",
  },
};

export default function BlogPage() {
  return <BlogPageClient />;
}
