import { Metada<PERSON> } from "next";
import { AboutHero } from "@/components/about/about-hero";
import { CompanyStory } from "@/components/about/company-story";
import { MissionVision } from "@/components/about/mission-vision";
import { AllInclusiveServices } from "@/components/about/all-inclusive-services";
import { AboutTestimonials } from "@/components/about/about-testimonials";
import { StarbucksFeature } from "@/components/about/starbucks-feature";
import { AboutContactForm } from "@/components/about/about-contact-form";

export const metadata: Metadata = {
  title: "About Us - Via Executive Suites | Professional Workspace Solutions",
  description: "Learn about Via Executive Suites' mission to provide affordable and professional workspace solutions for startups, small businesses, and freelancers in the Rio Grande Valley.",
  keywords: "about via executive suites, mission, vision, professional workspace, office space rental, rio grande valley, mcallen, edinburg",
  openGraph: {
    title: "About Us - Via Executive Suites",
    description: "Discover how Via Executive Suites delivers affordable and professional workspace solutions through four all-inclusive business centers in the RGV.",
    type: "website",
    locale: "en_US",
  },
};

export default function About() {
  return (
    <main className="min-h-screen">
      <AboutHero />
      <CompanyStory />
      <MissionVision />
      <AllInclusiveServices />
      <AboutTestimonials />
      <StarbucksFeature />
      <AboutContactForm />
    </main>
  );
}
