// Via Executive Suites - Button Styling Utilities
// This file provides consistent button styling with proper contrast ratios
// All styles ensure WCAG AA compliance for accessibility

export const buttonStyles = {
  // Primary buttons - for main actions (Via brand color #2d4f85)
  primary: {
    base: "bg-via-primary hover:bg-via-primary-dark text-white font-medium transition-colors duration-200",
    large: "bg-via-primary hover:bg-via-primary-dark text-white font-medium px-8 py-3 text-lg transition-colors duration-200",
    full: "w-full bg-via-primary hover:bg-via-primary-dark text-white font-medium transition-colors duration-200"
  },

  // Secondary buttons - for secondary actions (improved contrast)
  secondary: {
    base: "bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium border border-gray-300 transition-colors duration-200",
    large: "bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium border border-gray-300 px-8 py-3 text-lg transition-colors duration-200",
    full: "w-full bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium border border-gray-300 transition-colors duration-200"
  },

  // Outline buttons - for less prominent actions (improved contrast)
  outline: {
    primary: "border-2 border-via-primary bg-transparent text-via-primary hover:bg-via-primary hover:text-white font-medium transition-colors duration-200",
    white: "border-2 border-white bg-transparent text-white hover:bg-white hover:text-via-primary font-medium transition-colors duration-200",
    large: {
      primary: "border-2 border-via-primary bg-transparent text-via-primary hover:bg-via-primary hover:text-white font-medium px-8 py-3 text-lg transition-colors duration-200",
      white: "border-2 border-white bg-transparent text-white hover:bg-white hover:text-via-primary font-medium px-8 py-3 text-lg transition-colors duration-200"
    }
  },

  // Accent buttons - for beauty suites and special services (gold accent)
  accent: {
    base: "bg-via-accent hover:bg-via-accent-dark text-gray-900 font-medium transition-colors duration-200",
    large: "bg-via-accent hover:bg-via-accent-dark text-gray-900 font-medium px-8 py-3 text-lg transition-colors duration-200",
    full: "w-full bg-via-accent hover:bg-via-accent-dark text-gray-900 font-medium transition-colors duration-200"
  },

  // White buttons - for dark backgrounds (improved contrast)
  white: {
    base: "bg-white text-via-primary hover:bg-gray-100 font-medium border border-gray-200 transition-colors duration-200",
    large: "bg-white text-via-primary hover:bg-gray-100 font-medium border border-gray-200 px-8 py-3 text-lg transition-colors duration-200"
  },

  // Destructive buttons - for dangerous actions
  destructive: {
    base: "bg-red-600 hover:bg-red-700 text-white font-medium transition-colors duration-200",
    large: "bg-red-600 hover:bg-red-700 text-white font-medium px-8 py-3 text-lg transition-colors duration-200"
  }
};

// Helper function to get button styles with type safety
export function getButtonStyle(type: keyof typeof buttonStyles, variant?: string): string {
  const buttonType = buttonStyles[type];
  
  // Handle outline type which has a different structure
  if (type === 'outline') {
    if (variant && variant in buttonType) {
      const outlineType = buttonType as Record<string, string>;
      return outlineType[variant];
    }
    // Default to primary outline if no variant specified
    return (buttonType as Record<string, string>).primary;
  }
  
  // Handle other button types that have base property
  if (variant && typeof buttonType === 'object' && variant in buttonType) {
    return (buttonType as Record<string, string>)[variant];
  }
  
  // For types with base property (primary, secondary, accent, white, destructive)
  if ('base' in buttonType && typeof buttonType.base === 'string') {
    return buttonType.base;
  }
  
  // Final fallback - return empty string if no valid style found
  return '';
}

// Helper function to combine button styles with additional classes
export function combineButtonStyles(baseStyle: string, additionalClasses?: string): string {
  return additionalClasses ? `${baseStyle} ${additionalClasses}` : baseStyle;
}

// Common button combinations for different sections
export const commonButtons = {
  // Hero section buttons (for dark backgrounds)
  hero: {
    primary: buttonStyles.white.large,
    secondary: buttonStyles.outline.large.white
  },

  // Call-to-action section buttons
  cta: {
    primary: buttonStyles.primary.large,
    secondary: buttonStyles.outline.large.primary
  },

  // Pricing section buttons
  pricing: {
    primary: buttonStyles.primary.full,
    secondary: buttonStyles.secondary.full
  },

  // Service section buttons
  service: {
    primary: buttonStyles.primary.full,
    secondary: buttonStyles.outline.primary
  },

  // Form buttons
  form: {
    submit: buttonStyles.primary.full,
    cancel: buttonStyles.secondary.base,
    reset: buttonStyles.outline.primary
  },

  // Navigation buttons
  nav: {
    primary: buttonStyles.primary.base,
    secondary: buttonStyles.outline.primary,
    ghost: "text-via-primary hover:bg-via-primary/10 font-medium transition-colors duration-200"
  }
};

// Accessibility-focused button styles
export const accessibleButtons = {
  // High contrast versions for better accessibility
  highContrast: {
    primary: "bg-gray-900 hover:bg-gray-800 text-white font-bold border-2 border-gray-900 transition-colors duration-200",
    secondary: "bg-white hover:bg-gray-100 text-gray-900 font-bold border-2 border-gray-900 transition-colors duration-200"
  }
};
