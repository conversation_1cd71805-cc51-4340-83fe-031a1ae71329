export interface Location {
  name: string;
  fullName: string;
  address: string;
  phone: string;
  hours: string;
  email: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  description: string;
  features: string[];
  image: string;
}

export interface LocationSlug {
  slug: string;
}

export type LocationSlugs = 'adbc' | 'la-costa' | '23rd' | 'edinburg' | 'lindberg';

export interface LocationPageProps {
  params: Promise<{
    slug: string;
  }>;
}
