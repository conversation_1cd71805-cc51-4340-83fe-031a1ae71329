import Link from "next/link";
import Image from "next/image";
import { Facebook, Instagram, Linkedin, Youtube, Twitter } from "lucide-react";

const locations = [
  { name: "Via Edinburg", href: "/locations/edinburg" },
  { name: "La Costa", href: "/locations/la-costa" },
  { name: "Via 23rd", href: "/locations/23rd" },
  { name: "ADBC", href: "/locations/adbc" }
];

const services = [
  { name: "Virtual Offices", href: "/services/virtual-offices" },
  { name: "Beauty Suites", href: "/services/beauty-suites" },
  { name: "Executive Suites", href: "/services/executive-suites" }
];

const socialLinks = [
  { name: "Facebook", href: "https://www.facebook.com/viatosuccess/", icon: Facebook },
  { name: "Instagram", href: "https://www.instagram.com/viatosuccess/", icon: Instagram },
  { name: "LinkedIn", href: "https://www.linkedin.com/company/via-executive-suites/", icon: Linkedin },
  { name: "YouTube", href: "https://www.youtube.com/channel/UCzAaMIx4GQqXgLv7k_7meuA", icon: Youtube },
  { name: "Twitter", href: "https://twitter.com/viatosuccess", icon: Twitter }
];

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="mb-6">
              <Image
                src="https://via2success.com/wp-content/themes/viatosuccess_v1/assets/images/logo-footer.svg"
                alt="Via Executive Suites"
                width={200}
                height={60}
                className="h-16 w-auto filter brightness-0 invert"
              />
            </div>
            
            <p className="text-gray-300 text-sm leading-relaxed mb-6">
              Via Executive Suites has four all inclusive business centers strategically located in
              the Rio Grande Valley. So we take pride in helping entrepreneurs and startups. 
              And we also make it our mission to enhance the enterprise, identity, and integrity
              of our clients in order to further our region&apos;s economy.
            </p>
            
            <div className="text-sm text-gray-300">
              <div className="font-semibold text-white mb-1">Main Phone:</div>
              <div>(*************</div>
            </div>
          </div>

          {/* Locations */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Locations</h3>
            <ul className="space-y-2">
              {locations.map((location) => (
                <li key={location.name}>
                  <Link 
                    href={location.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                  >
                    {location.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Services</h3>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service.name}>
                  <Link 
                    href={service.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                  >
                    {service.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Follow Us */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Follow Us</h3>
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const IconComponent = social.icon;
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-white transition-colors duration-200"
                    aria-label={social.name}
                  >
                    <IconComponent className="w-5 h-5" />
                  </a>
                );
              })}
            </div>
            
            <div className="mt-6">
              <h4 className="text-sm font-semibold mb-2">Quick Links</h4>
              <ul className="space-y-1">
                <li>
                  <Link href="/about" className="text-gray-300 hover:text-white transition-colors duration-200 text-sm">
                    About
                  </Link>
                </li>
                <li>
                  <Link href="/blog" className="text-gray-300 hover:text-white transition-colors duration-200 text-sm">
                    Blog
                  </Link>
                </li>
                <li>
                  <Link href="/connect" className="text-gray-300 hover:text-white transition-colors duration-200 text-sm">
                    Connect
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-sm text-gray-400">
            &copy; 2025 Via Executive Suites. All rights reserved.
          </div>
          
          <div className="mt-4 md:mt-0">
            <Link 
              href="/privacy-policy" 
              className="text-sm text-gray-400 hover:text-white transition-colors duration-200"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
