import { Service, ServiceCategory } from '@/lib/types/services';

// Real service data - this would typically come from Supabase
export const servicesData: Service[] = [
  {
    id: 'executive-suites-001',
    slug: 'executive-suites',
    name: 'Executive Suites',
    shortDescription: 'Professional office spaces with premium amenities, perfect for established businesses and entrepreneurs seeking a prestigious address.',
    longDescription: 'Our executive suites provide everything you need to run a successful business without the hassle of traditional office management. From move-in ready spaces to comprehensive business services, we handle the details so you can focus on what matters most. Located in prime areas throughout the Rio Grande Valley, our suites offer prestigious addresses that enhance your business image while providing the flexibility to scale as your business grows.',
    heroImage: 'https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg',
    overviewImage: 'https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg',
    features: [
      {
        icon: 'Building2',
        title: 'Furnished Offices',
        description: 'Move-in ready offices with professional furniture and modern design'
      },
      {
        icon: 'Wifi',
        title: 'High-Speed Internet',
        description: 'Reliable, fast internet connectivity for seamless business operations'
      },
      {
        icon: 'Users',
        title: 'Reception Services',
        description: 'Professional reception staff to greet clients and handle inquiries'
      },
      {
        icon: 'Phone',
        title: 'Phone Systems',
        description: 'Professional phone answering and call forwarding services'
      },
      {
        icon: 'Car',
        title: 'Free Parking',
        description: 'Convenient parking for you and your clients'
      },
      {
        icon: 'Coffee',
        title: 'Break Room Access',
        description: 'Shared break rooms with coffee, tea, and refreshments'
      },
      {
        icon: 'Shield',
        title: 'Security & Access',
        description: '24/7 building access with secure entry systems'
      }
    ],
    pricingPlans: [
      {
        id: 'starter-suite',
        name: 'Starter Suite',
        price: 599,
        period: 'monthly',
        description: 'Perfect for solo entrepreneurs and small businesses',
        features: [
          '150-200 sq ft office',
          'Basic furniture included',
          'High-speed internet',
          'Reception services',
          'Mail handling',
          'Meeting room access (2 hours/month)'
        ],
        popular: false,
        sqft: 200,
        maxOccupancy: 2
      },
      {
        id: 'professional-suite',
        name: 'Professional Suite',
        price: 899,
        period: 'monthly',
        description: 'Ideal for growing businesses and small teams',
        features: [
          '250-350 sq ft office',
          'Premium furniture included',
          'High-speed internet',
          'Full reception services',
          'Phone answering service',
          'Meeting room access (5 hours/month)',
          'Kitchen access'
        ],
        popular: true,
        sqft: 350,
        maxOccupancy: 4
      },
      {
        id: 'executive-suite',
        name: 'Executive Suite',
        price: 1299,
        period: 'monthly',
        description: 'Premium space for established businesses',
        features: [
          '400-500 sq ft office',
          'Luxury furniture included',
          'High-speed internet',
          'Dedicated receptionist',
          'Full phone system',
          'Unlimited meeting room access',
          'Private kitchen access',
          'Priority parking'
        ],
        popular: false,
        sqft: 500,
        maxOccupancy: 6
      }
    ],
    amenities: [
      'Furnished offices',
      'High-speed internet',
      'Reception services',
      'Meeting rooms',
      'Phone systems',
      'Free parking',
      'Break rooms',
      'Security access',
      'Mail handling',
      'Kitchen access'
    ],
    locations: ['adbc', 'la-costa', '23rd', 'edinburg', 'lindberg'],
    category: 'executive-suites',
    isActive: true,
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  },
  {
    id: 'virtual-offices-001',
    slug: 'virtual-offices',
    name: 'Virtual Offices',
    shortDescription: 'Professional business address and mail handling services without the overhead of physical office space.',
    longDescription: 'Our virtual office solutions provide you with a prestigious business address and professional services without the commitment of physical office space. Perfect for remote workers, startups, and businesses looking to establish a presence in the Rio Grande Valley while maintaining flexibility. We handle your mail, answer your calls, and provide meeting room access when you need it.',
    heroImage: 'https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg',
    overviewImage: 'https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg',
    features: [
      {
        icon: 'MapPin',
        title: 'Professional Address',
        description: 'Prestigious business address for your company'
      },
      {
        icon: 'Mail',
        title: 'Mail Handling',
        description: 'Professional mail receiving and forwarding services'
      },
      {
        icon: 'Phone',
        title: 'Phone Answering',
        description: 'Professional call answering and forwarding'
      },
      {
        icon: 'Building2',
        title: 'Meeting Room Access',
        description: 'Access to professional meeting spaces when needed'
      },
      {
        icon: 'Users',
        title: 'Reception Services',
        description: 'Professional reception staff to greet your clients'
      },
      {
        icon: 'Clock',
        title: 'Flexible Plans',
        description: 'Monthly, quarterly, and annual plans available'
      }
    ],
    pricingPlans: [
      {
        id: 'basic-virtual',
        name: 'Basic Virtual Office',
        price: 99,
        period: 'monthly',
        description: 'Essential services for small businesses and entrepreneurs',
        features: [
          'Professional business address',
          'Mail receiving (up to 10 pieces/month)',
          'Mail forwarding (up to 5 pieces/month)',
          'Meeting room access (1 hour/month)',
          'Online account management'
        ],
        popular: false
      },
      {
        id: 'professional-virtual',
        name: 'Professional Virtual Office',
        price: 199,
        period: 'monthly',
        description: 'Comprehensive services for growing businesses',
        features: [
          'Professional business address',
          'Unlimited mail receiving',
          'Unlimited mail forwarding',
          'Phone answering service (20 calls/month)',
          'Meeting room access (5 hours/month)',
          'Reception services',
          'Online account management'
        ],
        popular: true
      },
      {
        id: 'premium-virtual',
        name: 'Premium Virtual Office',
        price: 399,
        period: 'monthly',
        description: 'Full-service virtual office with premium features',
        features: [
          'Professional business address',
          'Unlimited mail receiving',
          'Unlimited mail forwarding',
          'Unlimited phone answering',
          'Unlimited meeting room access',
          'Dedicated receptionist',
          'Priority booking for meeting rooms',
          'Online account management'
        ],
        popular: false
      }
    ],
    amenities: [
      'Professional business address',
      'Mail handling services',
      'Phone answering',
      'Meeting room access',
      'Reception services',
      'Online account management',
      'Mail forwarding',
      'Call forwarding'
    ],
    locations: ['adbc', 'la-costa', '23rd', 'edinburg', 'lindberg'],
    category: 'virtual-offices',
    isActive: true,
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  },
  {
    id: 'beauty-suites-001',
    slug: 'beauty-suites',
    name: 'Beauty Suites',
    shortDescription: 'Dedicated spaces for beauty professionals, salons, and wellness businesses with all the amenities you need.',
    longDescription: 'Our beauty suites are designed specifically for beauty professionals, salons, and wellness businesses. Each suite is equipped with the essential amenities you need to provide exceptional service to your clients. From hair salons to massage therapy, nail services to esthetics, our suites provide the perfect environment for your beauty business to thrive.',
    heroImage: 'https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg',
    overviewImage: 'https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg',
    features: [
      {
        icon: 'Scissors',
        title: 'Private Suites',
        description: 'Dedicated private spaces for your beauty services'
      },
      {
        icon: 'Zap',
        title: 'Utilities Included',
        description: 'All utilities included in your monthly rent'
      },
      {
        icon: 'Car',
        title: 'Free Parking',
        description: 'Convenient parking for you and your clients'
      },
      {
        icon: 'Clock',
        title: 'Flexible Terms',
        description: 'Monthly, quarterly, and annual lease options'
      },
      {
        icon: 'Shield',
        title: 'Security & Access',
        description: '24/7 building access with secure entry systems'
      },
      {
        icon: 'Heart',
        title: 'Client Amenities',
        description: 'Comfortable waiting areas and refreshments'
      }
    ],
    pricingPlans: [
      {
        id: 'beauty-starter',
        name: 'Beauty Starter Suite',
        price: 799,
        period: 'monthly',
        description: 'Perfect for individual beauty professionals',
        features: [
          '200-300 sq ft private suite',
          'Basic salon equipment included',
          'Utilities included',
          'Free parking',
          '24/7 access',
          'Waiting area access',
          'Basic security system'
        ],
        popular: false,
        sqft: 300,
        maxOccupancy: 2
      },
      {
        id: 'beauty-professional',
        name: 'Beauty Professional Suite',
        price: 1199,
        period: 'monthly',
        description: 'Ideal for established salons and beauty businesses',
        features: [
          '400-500 sq ft private suite',
          'Premium salon equipment included',
          'Utilities included',
          'Free parking',
          '24/7 access',
          'Private waiting area',
          'Advanced security system',
          'Kitchen access'
        ],
        popular: true,
        sqft: 500,
        maxOccupancy: 4
      },
      {
        id: 'beauty-premium',
        name: 'Beauty Premium Suite',
        price: 1699,
        period: 'monthly',
        description: 'Luxury suites for high-end beauty businesses',
        features: [
          '600-800 sq ft private suite',
          'Luxury salon equipment included',
          'Utilities included',
          'Free parking',
          '24/7 access',
          'Private waiting area',
          'Premium security system',
          'Private kitchen',
          'Priority parking',
          'Custom lighting options'
        ],
        popular: false,
        sqft: 800,
        maxOccupancy: 6
      }
    ],
    amenities: [
      'Private suites',
      'Utilities included',
      'Free parking',
      'Flexible lease terms',
      '24/7 access',
      'Security systems',
      'Waiting areas',
      'Kitchen access',
      'Salon equipment',
      'Client amenities'
    ],
    locations: ['adbc', 'la-costa', '23rd', 'edinburg', 'lindberg'],
    category: 'beauty-suites',
    isActive: true,
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  }
];

export const serviceCategories: ServiceCategory[] = [
  {
    id: 'executive-suites-cat',
    name: 'Executive Suites',
    slug: 'executive-suites',
    description: 'Professional office spaces with premium amenities, perfect for established businesses and entrepreneurs seeking a prestigious address.',
    icon: 'Building2',
    color: 'from-via-primary to-via-primary-dark',
    services: servicesData.filter(s => s.category === 'executive-suites')
  },
  {
    id: 'virtual-offices-cat',
    name: 'Virtual Offices',
    slug: 'virtual-offices',
    description: 'Professional business address and mail handling services without the overhead of physical office space.',
    icon: 'Monitor',
    color: 'from-via-primary-light to-via-primary',
    services: servicesData.filter(s => s.category === 'virtual-offices')
  },
  {
    id: 'beauty-suites-cat',
    name: 'Beauty Suites',
    slug: 'beauty-suites',
    description: 'Dedicated spaces for beauty professionals, salons, and wellness businesses with all the amenities you need.',
    icon: 'Scissors',
    color: 'from-via-accent to-via-accent-dark',
    services: servicesData.filter(s => s.category === 'beauty-suites')
  }
];

// Data access functions
export function getAllServices(): Service[] {
  return servicesData;
}

export function getServiceBySlug(slug: string): Service | undefined {
  return servicesData.find(service => service.slug === slug);
}

export function getServicesByCategory(category: string): Service[] {
  return servicesData.filter(service => service.category === category);
}

export function getAllServiceCategories(): ServiceCategory[] {
  return serviceCategories;
}

export function getServiceCategoryBySlug(slug: string): ServiceCategory | undefined {
  return serviceCategories.find(category => category.slug === slug);
}
