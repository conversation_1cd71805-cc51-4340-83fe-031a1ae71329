"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Phone } from "lucide-react";
import { CTASectionProps } from "@/lib/types/connect";
import { fadeInUp, staggerContainer, staggerItem } from "@/lib/animations";
import { cn } from "@/lib/utils";

export function AnimatedCTASection({
  title,
  subtitle,
  primaryButtonText,
  secondaryButtonText,
  onPrimaryClick,
  onSecondaryClick,
  className
}: CTASectionProps) {
  return (
    <motion.section
      className={cn("py-12 sm:py-16 lg:py-20 bg-via-primary text-white overflow-hidden relative", className)}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      variants={staggerContainer}
    >
      {/* Animated background elements */}
      <motion.div
        className="absolute inset-0 opacity-5"
        initial={{ scale: 0.8, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 0.05 }}
        transition={{ duration: 2, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <div className="absolute top-10 left-10 w-24 h-24 bg-white rounded-full blur-2xl"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-white rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/3 w-20 h-20 bg-white rounded-full blur-2xl"></div>
      </motion.div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.h2
          className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4"
          variants={fadeInUp}
        >
          {title}
        </motion.h2>

        <motion.p
          className="text-lg sm:text-xl text-via-primary-light mb-6 sm:mb-8 max-w-2xl mx-auto px-4"
          variants={fadeInUp}
        >
          {subtitle}
        </motion.p>

        <motion.div
          className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4"
          variants={staggerItem}
        >
          <motion.div
            whileHover={{
              scale: 1.05,
              boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)"
            }}
            whileTap={{ scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="w-full sm:w-auto"
          >
            <Button
              size="lg"
              className="w-full sm:w-auto bg-white text-via-primary hover:bg-gray-100 px-6 sm:px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
              onClick={onPrimaryClick}
            >
              <Calendar className="mr-2 h-4 sm:h-5 w-4 sm:w-5" />
              {primaryButtonText}
            </Button>
          </motion.div>

          <motion.div
            whileHover={{
              scale: 1.05,
              boxShadow: "0 10px 25px rgba(255, 255, 255, 0.1)"
            }}
            whileTap={{ scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="w-full sm:w-auto"
          >
            <Button
              size="lg"
              className="w-full sm:w-auto border-2 border-white text-white hover:bg-white hover:text-via-primary px-6 sm:px-8 py-3 transition-all duration-300"
              onClick={onSecondaryClick}
            >
              <Phone className="mr-2 h-4 sm:h-5 w-4 sm:w-5" />
              {secondaryButtonText}
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </motion.section>
  );
}
