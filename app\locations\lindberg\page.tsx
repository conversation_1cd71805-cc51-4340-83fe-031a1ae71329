"use client";

import { use } from "react";
import { notFound } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { MapPin, Phone, Clock, Mail, Building2 } from "lucide-react";

// Location data for Lindberg
const locationData = {
  name: "Lindberg",
  fullName: "Via Executive Suites Lindberg",
  address: "1234 Lindberg Ave, McAllen, Texas 78501",
  phone: "(*************",
  hours: "Monday - Friday: 8:00 AM - 6:00 PM",
  email: "<EMAIL>",
  coordinates: {
    lat: 26.2034,
    lng: -98.2300
  },
  description: "Our newest location in the vibrant Lindberg area of McAllen, offering modern executive suites with premium amenities and convenient access to major highways and business districts.",
  features: [
    "Modern office spaces",
    "High-speed internet",
    "Meeting rooms",
    "Reception services",
    "Free parking",
    "24/7 building access",
    "Kitchen facilities",
    "Business center"
  ],
  image: "https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg"
};

interface LocationPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default function LindbergLocationPage({ params }: LocationPageProps) {
  // Unwrap the params Promise using React.use() for Next.js 15 compatibility
  const resolvedParams = use(params) as { slug: string };
  
  // Verify this is the correct page
  if (resolvedParams.slug !== 'lindberg') {
    notFound();
  }

  const location = locationData;

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-24 lg:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            {location.fullName}
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
            {location.description}
          </p>
          <div className="mt-8">
            <Button size="lg" className="bg-white text-via-primary hover:bg-gray-100 px-8 py-3 mr-4">
              Schedule a Tour
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-via-primary px-8 py-3">
              Contact Us
            </Button>
          </div>
        </div>
      </section>

      {/* Location Details */}
      <section className="py-16 bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                About This Location
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Located in the heart of the Lindberg area, our newest executive suites location offers modern, professional office spaces designed for today&apos;s business needs. With easy access to major highways and proximity to key business districts, this location provides the perfect balance of convenience and professionalism.
              </p>
              <p className="text-lg text-gray-600 mb-8">
                Our Lindberg location features state-of-the-art facilities, premium amenities, and a professional environment that will enhance your business image and productivity.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-via-primary mr-3" />
                  <span className="text-gray-700">{location.address}</span>
                </div>
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-via-primary mr-3" />
                  <span className="text-gray-700">{location.phone}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-via-primary mr-3" />
                  <span className="text-gray-700">{location.hours}</span>
                </div>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-via-primary mr-3" />
                  <span className="text-gray-700">{location.email}</span>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="aspect-w-4 aspect-h-3 rounded-lg overflow-hidden shadow-xl">
                <Image
                  src={location.image}
                  alt={`${location.name} executive suites location`}
                  width={800}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Location Features & Amenities
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to run a successful business, all in one convenient location.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {location.features.map((feature) => (
              <div key={feature} className="bg-white p-6 rounded-lg shadow-md border border-gray-200 text-center">
                <div className="w-12 h-12 bg-via-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Building2 className="h-6 w-6 text-via-primary" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">{feature}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-via-primary text-white overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Experience Lindberg?
          </h2>
          <p className="text-xl text-via-primary-light mb-8 max-w-2xl mx-auto">
            Schedule a tour of our Lindberg location and discover how our executive suites can elevate your business to new heights.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-via-primary hover:bg-gray-100 px-8 py-3">
              Schedule a Tour
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-via-primary px-8 py-3">
              Contact Us
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
