import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Building2, Globe, Scissors } from "lucide-react";

const workspaceTypes = [
  {
    icon: Building2,
    title: "Executive Suites",
    description: "Our conveniently located Executive Suites offer office space plus shared amenities such as lobbies, reception areas, front desk services, break rooms, conference rooms.",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_5-img_1.jpg",
    href: "/services/executive-suites"
  },
  {
    icon: Globe,
    title: "Virtual Offices",
    description: "Our Virtual Office is an excellent option to create a professional image, business address, or if you're just looking to operate with a tight budget, we can get you set up in a matter of minutes.",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_5-img_2.jpg",
    href: "/services/virtual-offices"
  },
  {
    icon: Scissors,
    title: "Health & Beauty Suites",
    description: "Our Health and Beauty Suites provide state-of-the-art facilities for your health and beauty business. Comfortable waiting areas, beautiful work areas, front-desk staff, weekend hours, and a single, affordable, all-inclusive price help your business succeed.",
    image: "https://via2success.com/wp-content/uploads/2021/01/home-section_5-img_3.jpg",
    href: "/services/beauty-suites"
  }
];

export function WorkspaceTypes() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Workspace Types
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Our four strategic locations offer a variety of suites designed to meet your specific business needs.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {workspaceTypes.map((workspace, index) => {
            const IconComponent = workspace.icon;
            return (
              <div 
                key={index}
                className="bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group"
              >
                {/* Image */}
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={workspace.image}
                    alt={workspace.title}
                    width={400}
                    height={300}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300"></div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full mr-4">
                      <IconComponent className="w-6 h-6 text-primary" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      {workspace.title}
                    </h3>
                  </div>
                  
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {workspace.description}
                  </p>
                  
                  <Link href={workspace.href}>
                    <Button 
                      variant="outline"
                      className="w-full border-primary text-primary hover:bg-primary hover:text-white transition-all duration-300"
                    >
                      Learn More
                    </Button>
                  </Link>
                </div>
              </div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-12">
          <Link href="/services">
            <Button 
              size="lg"
              className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300 transform hover:scale-105"
            >
              View All Workspaces
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
