"use client";

import { useState, useCallback } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { MessageCircle, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { 
  ContactFormData, 
  ContactFormErrors, 
  FormSubmissionState,
  ContactFormProps 
} from "@/lib/types/connect";
import { staggerContainer, staggerItem } from "@/lib/animations";
import { cn } from "@/lib/utils";

// Form validation utility
const validateForm = (data: ContactFormData): ContactFormErrors => {
  const errors: ContactFormErrors = {};

  if (!data.firstName.trim()) {
    errors.firstName = "First name is required";
  } else if (data.firstName.length < 2) {
    errors.firstName = "First name must be at least 2 characters";
  }

  if (!data.lastName.trim()) {
    errors.lastName = "Last name is required";
  } else if (data.lastName.length < 2) {
    errors.lastName = "Last name must be at least 2 characters";
  }

  if (!data.email.trim()) {
    errors.email = "Email is required";
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.email = "Please enter a valid email address";
  }

  if (data.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
    errors.phone = "Please enter a valid phone number";
  }

  if (!data.message.trim()) {
    errors.message = "Message is required";
  } else if (data.message.length < 10) {
    errors.message = "Message must be at least 10 characters";
  }

  return errors;
};

export function EnhancedContactForm({ 
  onSubmit, 
  className,
  showCompanyField = true,
  showPhoneField = true 
}: ContactFormProps) {
  const [formData, setFormData] = useState<ContactFormData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    company: "",
    message: ""
  });

  const [errors, setErrors] = useState<ContactFormErrors>({});
  const [submissionState, setSubmissionState] = useState<FormSubmissionState>({
    isLoading: false,
    isSuccess: false,
    error: null
  });

  const handleInputChange = useCallback((
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev: ContactFormData) => ({ ...prev, [name]: value }));
    
    // Clear error for this field when user starts typing
    if (errors[name as keyof ContactFormErrors]) {
      setErrors((prev: ContactFormErrors) => ({ ...prev, [name]: undefined }));
    }
  }, [errors]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const validationErrors = validateForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setSubmissionState({ isLoading: true, isSuccess: false, error: null });

    try {
      if (onSubmit) {
        await onSubmit(formData);
      } else {
        // Default submission logic
        await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
        console.log("Form submitted:", formData);
      }
      
      setSubmissionState({ isLoading: false, isSuccess: true, error: null });
      
      // Reset form after successful submission
      setTimeout(() => {
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
          company: "",
          message: ""
        });
        setSubmissionState({ isLoading: false, isSuccess: false, error: null });
      }, 3000);
      
    } catch (error) {
      setSubmissionState({
        isLoading: false,
        isSuccess: false,
        error: error instanceof Error ? error.message : "An error occurred"
      });
    }
  };

  return (
    <motion.section
      className={cn("py-12 sm:py-16 lg:py-20 bg-gray-50 overflow-hidden", className)}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      variants={staggerContainer}
    >
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-8 sm:mb-12"
          variants={staggerItem}
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Send Us a Message
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            Have questions? We&apos;d love to hear from you. Send us a message and we&apos;ll respond as soon as possible.
          </p>
        </motion.div>

        <motion.form
          onSubmit={handleSubmit}
          className="bg-white rounded-lg shadow-lg p-6 sm:p-8"
          variants={staggerItem}
        >
          {/* Success Message */}
          {submissionState.isSuccess && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md flex items-center gap-3"
            >
              <CheckCircle className="h-5 w-5 text-green-600" />
              <p className="text-green-800">Thank you! Your message has been sent successfully.</p>
            </motion.div>
          )}

          {/* Error Message */}
          {submissionState.error && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md flex items-center gap-3"
            >
              <AlertCircle className="h-5 w-5 text-red-600" />
              <p className="text-red-800">{submissionState.error}</p>
            </motion.div>
          )}

          {/* Name Fields */}
          <div className="grid sm:grid-cols-2 gap-4 sm:gap-6 mb-6">
            <div>
              <Label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                First Name *
              </Label>
              <Input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                className={cn(
                  "w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent",
                  errors.firstName ? "border-red-300" : "border-gray-300"
                )}
                disabled={submissionState.isLoading}
              />
              {errors.firstName && (
                <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>
              )}
            </div>
            <div>
              <Label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                Last Name *
              </Label>
              <Input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                className={cn(
                  "w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent",
                  errors.lastName ? "border-red-300" : "border-gray-300"
                )}
                disabled={submissionState.isLoading}
              />
              {errors.lastName && (
                <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>
              )}
            </div>
          </div>

          {/* Email and Phone Fields */}
          <div className="grid sm:grid-cols-2 gap-4 sm:gap-6 mb-6">
            <div>
              <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email *
              </Label>
              <Input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={cn(
                  "w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent",
                  errors.email ? "border-red-300" : "border-gray-300"
                )}
                disabled={submissionState.isLoading}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>
            {showPhoneField && (
              <div>
                <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone
                </Label>
                <Input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className={cn(
                    "w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent",
                    errors.phone ? "border-red-300" : "border-gray-300"
                  )}
                  disabled={submissionState.isLoading}
                />
                {errors.phone && (
                  <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                )}
              </div>
            )}
          </div>

          {/* Company Field */}
          {showCompanyField && (
            <div className="mb-6">
              <Label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                Company
              </Label>
              <Input
                type="text"
                id="company"
                name="company"
                value={formData.company}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent"
                disabled={submissionState.isLoading}
              />
            </div>
          )}

          {/* Message Field */}
          <div className="mb-6">
            <Label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
              Message *
            </Label>
            <Textarea
              id="message"
              name="message"
              rows={5}
              value={formData.message}
              onChange={handleInputChange}
              className={cn(
                "w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent",
                errors.message ? "border-red-300" : "border-gray-300"
              )}
              placeholder="Tell us about your business needs and how we can help..."
              disabled={submissionState.isLoading}
            />
            {errors.message && (
              <p className="mt-1 text-sm text-red-600">{errors.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <div className="text-center">
            <Button 
              type="submit" 
              size="lg" 
              className="bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3 disabled:opacity-50"
              disabled={submissionState.isLoading}
            >
              {submissionState.isLoading ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <MessageCircle className="mr-2 h-5 w-5" />
                  Send Message
                </>
              )}
            </Button>
          </div>
        </motion.form>
      </div>
    </motion.section>
  );
}
