---
alwaysApply: false
---

# Via Workspace Platform - Project Intelligence

## Project Overview
This is a comprehensive workspace booking platform for Via Executive Suites, built with Next.js 15, Supabase, and TypeScript. The project follows a 5-phase development approach over 8-10 months.

## Key Technical Patterns

### Architecture
- **Next.js 15 App Router:** Use server components where possible, client components only when necessary
- **Supabase Backend:** Leverage PostgreSQL, Auth, Storage, Edge Functions, and Realtime
- **TypeScript Strict Mode:** Full type safety with no `any` types allowed
- **Atomic Design:** Components follow Atoms → Molecules → Organisms → Templates → Pages pattern

### Component Structure
- **UI Components:** Use shadcn/ui as the foundation, extend with Via branding
- **Form Components:** Implement with React Hook Form + Zod validation
- **Layout Components:** Create reusable layout patterns for consistency
- **Feature Components:** Group related functionality in feature folders

### State Management
- **Zustand:** Use for global state management (user, auth, app state)
- **Supabase Subscriptions:** Real-time updates for availability and bookings
- **Local State:** React useState for component-specific state
- **Server State:** Supabase queries with proper caching strategies

### Database Patterns
- **Table Naming:** All tables prefixed with `via_` (e.g., `via_profiles`, `via_locations`)
- **Row Level Security:** Implement RLS policies for all user-facing tables
- **Relationships:** Use proper foreign keys and junction tables for complex relationships
- **Indexing:** Create indexes for search, filtering, and performance-critical queries

## Development Guidelines

### Code Quality
- **ESLint:** All code must pass ESLint with no warnings
- **TypeScript:** Use strict mode, avoid type assertions, prefer interfaces over types
- **Naming:** Use descriptive names, follow camelCase for variables/functions, PascalCase for components
- **Comments:** Document complex business logic and non-obvious decisions

### Performance
- **Page Load:** Target <3 seconds for all pages
- **API Response:** Target <500ms for all API endpoints
- **Bundle Size:** Monitor and optimize bundle size regularly
- **Images:** Use Next.js Image component with proper optimization

### Security
- **Authentication:** Use Supabase Auth with proper session management
- **Authorization:** Implement RLS policies for all database access
- **Input Validation:** Validate all user inputs with Zod schemas
- **HTTPS:** Ensure all communications use HTTPS

## Via Brand Implementation

### Design System
- **Colors:** Implement Via brand colors as CSS custom properties
- **Typography:** Use consistent font hierarchy and sizing
- **Spacing:** Follow 8px grid system for consistent spacing
- **Components:** All components should reflect Via's premium positioning

### Brand Elements
- **Logo:** Via logo should be prominent in header and key locations
- **Language:** Use professional, premium language throughout
- **Imagery:** High-quality workspace and professional environment images
- **Tone:** Professional, trustworthy, and innovative

## Database Schema Guidelines

### Core Tables
- **via_profiles:** Extended user profiles with membership tiers
- **via_locations:** Workspace locations with geographic data
- **via_workspaces:** Individual workspace units with pricing
- **via_bookings:** Booking records with status tracking
- **via_payments:** Payment transactions with Stripe integration

### Data Relationships
- **Users → Profiles:** One-to-one relationship with extended profile data
- **Locations → Workspaces:** One-to-many relationship for workspace types
- **Users ↔ Workspaces:** Many-to-many through bookings table
- **Bookings → Payments:** One-to-one relationship for transaction tracking

## Testing Strategy

### Testing Levels
- **Unit Tests:** Test individual components and functions
- **Integration Tests:** Test Supabase operations and API endpoints
- **E2E Tests:** Test complete user journeys with Playwright
- **Performance Tests:** Monitor Core Web Vitals and Lighthouse scores

### Testing Tools
- **Jest:** Unit and integration testing framework
- **React Testing Library:** Component testing utilities
- **Playwright:** End-to-end testing
- **MSW:** API mocking for testing

## Deployment & Infrastructure

### Environment Management
- **Development:** Local development with proper environment variables
- **Staging:** Supabase staging project for testing
- **Production:** Supabase production project with monitoring

### CI/CD Pipeline
- **GitHub Actions:** Automated testing and deployment
- **Vercel:** Frontend deployment with previews
- **Supabase:** Database migrations and edge function deployment

## Common Patterns & Solutions

### Authentication Flow
```typescript
// Use Supabase Auth with proper error handling
const { data, error } = await supabase.auth.signInWithPassword({
  email,
  password
});

if (error) {
  // Handle authentication errors
  throw new Error(error.message);
}
```

### Database Queries
```typescript
// Use proper error handling and type safety
const { data, error } = await supabase
  .from('via_profiles')
  .select('*')
  .eq('user_id', userId)
  .single();

if (error) {
  // Handle database errors
  console.error('Database error:', error);
  return null;
}
```

### Component Structure
```typescript
// Follow consistent component structure
interface ComponentProps {
  // Define clear prop interfaces
}

export function ComponentName({ prop1, prop2 }: ComponentProps) {
  // Component implementation
  return (
    <div>
      {/* Component JSX */}
    </div>
  );
}
```

## Phase-Specific Guidelines

### Phase 1: Foundation
- Focus on Via branding and user experience
- Implement solid database foundation
- Create reusable component library
- Establish testing infrastructure

### Phase 2: Workspace Discovery
- Implement location management system
- Create advanced search functionality
- Integrate Google Maps API
- Build workspace showcase features

### Phase 3: Booking Platform
- Develop complete booking flow
- Integrate Stripe payment processing
- Implement real-time availability
- Create calendar and scheduling system

### Phase 4: Intelligence & Optimization
- Build AI recommendation engine
- Implement analytics and reporting
- Create partner portal
- Optimize performance and user experience

### Phase 5: Advanced Features
- Develop AI assistant chatbot
- Create RESTful API platform
- Implement PWA capabilities
- Add multi-language support

## Important Notes

### Dependencies
- Add Zustand for state management
- Add React Hook Form for form handling
- Add Zod for validation schemas
- Add Google Maps API for Phase 2
- Add Stripe for Phase 3

### Current Status
- Project is in Phase 1 (Foundation)
- Basic Next.js + Supabase setup complete
- Memory Bank system established
- Ready to begin Via brand implementation

### Next Steps
1. Implement Via branding across all components
2. Design and implement database schema
3. Create user profile and membership system
4. Build core UI components with Via design
5. Set up testing infrastructure

## Resources & References

### Documentation
- Memory Bank: Complete project documentation in memory-bank/ folder
- Supabase Docs: https://supabase.com/docs
- Next.js Docs: https://nextjs.org/docs
- Tailwind CSS: https://tailwindcss.com/docs

### Project Files
- Memory Bank: memory-bank/ folder contains all project context
- Components: components/ folder for reusable UI components
- Database: lib/supabase/ for database configuration
- Types: lib/types/ for TypeScript type definitions
# Via Workspace Platform - Project Intelligence

## Project Overview
This is a comprehensive workspace booking platform for Via Executive Suites, built with Next.js 15, Supabase, and TypeScript. The project follows a 5-phase development approach over 8-10 months.

## Key Technical Patterns

### Architecture
- **Next.js 15 App Router:** Use server components where possible, client components only when necessary
- **Supabase Backend:** Leverage PostgreSQL, Auth, Storage, Edge Functions, and Realtime
- **TypeScript Strict Mode:** Full type safety with no `any` types allowed
- **Atomic Design:** Components follow Atoms → Molecules → Organisms → Templates → Pages pattern

### Component Structure
- **UI Components:** Use shadcn/ui as the foundation, extend with Via branding
- **Form Components:** Implement with React Hook Form + Zod validation
- **Layout Components:** Create reusable layout patterns for consistency
- **Feature Components:** Group related functionality in feature folders

### State Management
- **Zustand:** Use for global state management (user, auth, app state)
- **Supabase Subscriptions:** Real-time updates for availability and bookings
- **Local State:** React useState for component-specific state
- **Server State:** Supabase queries with proper caching strategies

### Database Patterns
- **Table Naming:** All tables prefixed with `via_` (e.g., `via_profiles`, `via_locations`)
- **Row Level Security:** Implement RLS policies for all user-facing tables
- **Relationships:** Use proper foreign keys and junction tables for complex relationships
- **Indexing:** Create indexes for search, filtering, and performance-critical queries

## Development Guidelines

### Code Quality
- **ESLint:** All code must pass ESLint with no warnings
- **TypeScript:** Use strict mode, avoid type assertions, prefer interfaces over types
- **Naming:** Use descriptive names, follow camelCase for variables/functions, PascalCase for components
- **Comments:** Document complex business logic and non-obvious decisions

### Performance
- **Page Load:** Target <3 seconds for all pages
- **API Response:** Target <500ms for all API endpoints
- **Bundle Size:** Monitor and optimize bundle size regularly
- **Images:** Use Next.js Image component with proper optimization

### Security
- **Authentication:** Use Supabase Auth with proper session management
- **Authorization:** Implement RLS policies for all database access
- **Input Validation:** Validate all user inputs with Zod schemas
- **HTTPS:** Ensure all communications use HTTPS

## Via Brand Implementation

### Design System
- **Colors:** Implement Via brand colors as CSS custom properties
- **Typography:** Use consistent font hierarchy and sizing
- **Spacing:** Follow 8px grid system for consistent spacing
- **Components:** All components should reflect Via's premium positioning

### Brand Elements
- **Logo:** Via logo should be prominent in header and key locations
- **Language:** Use professional, premium language throughout
- **Imagery:** High-quality workspace and professional environment images
- **Tone:** Professional, trustworthy, and innovative

## Database Schema Guidelines

### Core Tables
- **via_profiles:** Extended user profiles with membership tiers
- **via_locations:** Workspace locations with geographic data
- **via_workspaces:** Individual workspace units with pricing
- **via_bookings:** Booking records with status tracking
- **via_payments:** Payment transactions with Stripe integration

### Data Relationships
- **Users → Profiles:** One-to-one relationship with extended profile data
- **Locations → Workspaces:** One-to-many relationship for workspace types
- **Users ↔ Workspaces:** Many-to-many through bookings table
- **Bookings → Payments:** One-to-one relationship for transaction tracking

## Testing Strategy

### Testing Levels
- **Unit Tests:** Test individual components and functions
- **Integration Tests:** Test Supabase operations and API endpoints
- **E2E Tests:** Test complete user journeys with Playwright
- **Performance Tests:** Monitor Core Web Vitals and Lighthouse scores

### Testing Tools
- **Jest:** Unit and integration testing framework
- **React Testing Library:** Component testing utilities
- **Playwright:** End-to-end testing
- **MSW:** API mocking for testing

## Deployment & Infrastructure

### Environment Management
- **Development:** Local development with proper environment variables
- **Staging:** Supabase staging project for testing
- **Production:** Supabase production project with monitoring

### CI/CD Pipeline
- **GitHub Actions:** Automated testing and deployment
- **Vercel:** Frontend deployment with previews
- **Supabase:** Database migrations and edge function deployment

## Common Patterns & Solutions

### Authentication Flow
```typescript
// Use Supabase Auth with proper error handling
const { data, error } = await supabase.auth.signInWithPassword({
  email,
  password
});

if (error) {
  // Handle authentication errors
  throw new Error(error.message);
}
```

### Database Queries
```typescript
// Use proper error handling and type safety
const { data, error } = await supabase
  .from('via_profiles')
  .select('*')
  .eq('user_id', userId)
  .single();

if (error) {
  // Handle database errors
  console.error('Database error:', error);
  return null;
}
```

### Component Structure
```typescript
// Follow consistent component structure
interface ComponentProps {
  // Define clear prop interfaces
}

export function ComponentName({ prop1, prop2 }: ComponentProps) {
  // Component implementation
  return (
    <div>
      {/* Component JSX */}
    </div>
  );
}
```

## Phase-Specific Guidelines

### Phase 1: Foundation
- Focus on Via branding and user experience
- Implement solid database foundation
- Create reusable component library
- Establish testing infrastructure

### Phase 2: Workspace Discovery
- Implement location management system
- Create advanced search functionality
- Integrate Google Maps API
- Build workspace showcase features

### Phase 3: Booking Platform
- Develop complete booking flow
- Integrate Stripe payment processing
- Implement real-time availability
- Create calendar and scheduling system

### Phase 4: Intelligence & Optimization
- Build AI recommendation engine
- Implement analytics and reporting
- Create partner portal
- Optimize performance and user experience

### Phase 5: Advanced Features
- Develop AI assistant chatbot
- Create RESTful API platform
- Implement PWA capabilities
- Add multi-language support

## Important Notes

### Dependencies
- Add Zustand for state management
- Add React Hook Form for form handling
- Add Zod for validation schemas
- Add Google Maps API for Phase 2
- Add Stripe for Phase 3

### Current Status
- Project is in Phase 1 (Foundation)
- Basic Next.js + Supabase setup complete
- Memory Bank system established
- Ready to begin Via brand implementation

### Next Steps
1. Implement Via branding across all components
2. Design and implement database schema
3. Create user profile and membership system
4. Build core UI components with Via design
5. Set up testing infrastructure

## Resources & References

### Documentation
- Memory Bank: Complete project documentation in memory-bank/ folder
- Supabase Docs: https://supabase.com/docs
- Next.js Docs: https://nextjs.org/docs
- Tailwind CSS: https://tailwindcss.com/docs

### Project Files
- Memory Bank: memory-bank/ folder contains all project context
- Components: components/ folder for reusable UI components
- Database: lib/supabase/ for database configuration
- Types: lib/types/ for TypeScript type definitions
