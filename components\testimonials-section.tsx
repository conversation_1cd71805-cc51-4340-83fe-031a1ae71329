import { Quote } from "lucide-react";

const testimonials = [
  {
    quote: "That moment when you realize everything will be just fine in our new location! I received a call from <PERSON><PERSON><PERSON>, the manager at ADBC, to tell me new customers are looking for us already in the new location!! He forwarded the customer to us! Thank you <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> for all your good customer service. Looking forward for great business for all.",
    author: "Vital Camera Mary",
    company: "Vital Camera",
    location: "ADBC Location"
  },
  {
    quote: "<PERSON><PERSON><PERSON> and all the staff at ADBC have been extremely helpful since I started Pronto Credit in McAllen on 2011.",
    author: "<PERSON>",
    company: "Pronto Credit",
    location: "ADBC Location"
  }
];

export function TestimonialsSection() {
  return (
    <section className="py-16 bg-primary text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Testimonials
          </h2>
          <p className="text-lg text-blue-100 max-w-3xl mx-auto">
            Hear what our clients have to say about their experience with Via Executive Suites
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="bg-white/10 backdrop-blur-sm rounded-lg p-8 hover:bg-white/20 transition-all duration-300"
            >
              {/* Quote Icon */}
              <div className="flex justify-center mb-6">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 rounded-full">
                  <Quote className="w-6 h-6 text-white" />
                </div>
              </div>

              {/* Quote Text */}
              <blockquote className="text-center mb-6">
                <p className="text-lg leading-relaxed text-white/90 italic">
                  &ldquo;{testimonial.quote}&rdquo;
                </p>
              </blockquote>

              {/* Attribution */}
              <div className="text-center">
                <div className="font-semibold text-white text-lg">
                  {testimonial.author}
                </div>
                <div className="text-blue-200 text-sm">
                  {testimonial.company}
                </div>
                <div className="text-blue-300 text-xs mt-1">
                  {testimonial.location}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Testimonial Feature */}
        <div className="mt-12 text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 max-w-4xl mx-auto">
            <div className="flex items-center justify-center mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-400 rounded-full">
                <svg className="w-8 h-8 text-yellow-800" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
            </div>
            
            <h3 className="text-2xl font-bold mb-4">
              Complimentary Starbucks Coffee
            </h3>
            
            <p className="text-lg text-white/90">
              Enjoy complimentary Starbucks coffee at any of our four locations.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
